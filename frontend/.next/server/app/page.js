/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?3a46\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRnNlbnRpbmVsYS1pYSUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGcHJvdmlkZXJzLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRnNlbnRpbmVsYS1pYSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRnNlbnRpbmVsYS1pYSUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VudGluZWxhLWZyb250ZW5kLz9hMGU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZXhhbmRyZXNpbWFzbWFjaWVsL0RvY3VtZW50cy9pYS1zaXN0ZW1hcy9zZW50aW5lbGEtaWEvZnJvbnRlbmQvc3JjL2FwcC9wcm92aWRlcnMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FSidebar.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FSidebar.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/Dashboard.tsx */ \"(ssr)/./src/components/dashboard/Dashboard.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Sidebar.tsx */ \"(ssr)/./src/components/layout/Sidebar.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRnNlbnRpbmVsYS1pYSUyRmZyb250ZW5kJTJGc3JjJTJGY29tcG9uZW50cyUyRmRhc2hib2FyZCUyRkRhc2hib2FyZC50c3gmbW9kdWxlcz0lMkZVc2VycyUyRmFsZXhhbmRyZXNpbWFzbWFjaWVsJTJGRG9jdW1lbnRzJTJGaWEtc2lzdGVtYXMlMkZzZW50aW5lbGEtaWElMkZmcm9udGVuZCUyRnNyYyUyRmNvbXBvbmVudHMlMkZsYXlvdXQlMkZIZWFkZXIudHN4Jm1vZHVsZXM9JTJGVXNlcnMlMkZhbGV4YW5kcmVzaW1hc21hY2llbCUyRkRvY3VtZW50cyUyRmlhLXNpc3RlbWFzJTJGc2VudGluZWxhLWlhJTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGU2lkZWJhci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFtSjtBQUNuSixnTEFBNkk7QUFDN0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW50aW5lbGEtZnJvbnRlbmQvPzVhM2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxleGFuZHJlc2ltYXNtYWNpZWwvRG9jdW1lbnRzL2lhLXNpc3RlbWFzL3NlbnRpbmVsYS1pYS9mcm9udGVuZC9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZXhhbmRyZXNpbWFzbWFjaWVsL0RvY3VtZW50cy9pYS1zaXN0ZW1hcy9zZW50aW5lbGEtaWEvZnJvbnRlbmQvc3JjL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGV4YW5kcmVzaW1hc21hY2llbC9Eb2N1bWVudHMvaWEtc2lzdGVtYXMvc2VudGluZWxhLWlhL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL2xheW91dC9TaWRlYmFyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FSidebar.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        disableTransitionOnChange: true,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#22c55e\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/providers.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/providers.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTRDO0FBQ0Y7QUFFbkMsU0FBU0UsVUFBVSxFQUFFQyxRQUFRLEVBQWlDO0lBQ25FLHFCQUNFLDhEQUFDSCxzREFBYUE7UUFDWkksV0FBVTtRQUNWQyxjQUFhO1FBQ2JDLFlBQVk7UUFDWkMseUJBQXlCOztZQUV4Qko7MEJBQ0QsOERBQUNGLG9EQUFPQTtnQkFDTk8sVUFBUztnQkFDVEMsY0FBYztvQkFDWkMsVUFBVTtvQkFDVkMsT0FBTzt3QkFDTEMsWUFBWTt3QkFDWkMsT0FBTztvQkFDVDtvQkFDQUMsU0FBUzt3QkFDUEosVUFBVTt3QkFDVkssV0FBVzs0QkFDVEMsU0FBUzs0QkFDVEMsV0FBVzt3QkFDYjtvQkFDRjtvQkFDQUMsT0FBTzt3QkFDTFIsVUFBVTt3QkFDVkssV0FBVzs0QkFDVEMsU0FBUzs0QkFDVEMsV0FBVzt3QkFDYjtvQkFDRjtnQkFDRjs7Ozs7Ozs7Ozs7O0FBSVIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW50aW5lbGEtZnJvbnRlbmQvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3g/OTMyNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcyc7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAncmVhY3QtaG90LXRvYXN0JztcblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiAoXG4gICAgPFRoZW1lUHJvdmlkZXJcbiAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcbiAgICAgIGRlZmF1bHRUaGVtZT1cInN5c3RlbVwiXG4gICAgICBlbmFibGVTeXN0ZW1cbiAgICAgIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2VcbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgICA8VG9hc3RlclxuICAgICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICAgIGR1cmF0aW9uOiA0MDAwLFxuICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzM2MzYzNicsXG4gICAgICAgICAgICBjb2xvcjogJyNmZmYnLFxuICAgICAgICAgIH0sXG4gICAgICAgICAgc3VjY2Vzczoge1xuICAgICAgICAgICAgZHVyYXRpb246IDMwMDAsXG4gICAgICAgICAgICBpY29uVGhlbWU6IHtcbiAgICAgICAgICAgICAgcHJpbWFyeTogJyMyMmM1NWUnLFxuICAgICAgICAgICAgICBzZWNvbmRhcnk6ICcjZmZmJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgICBlcnJvcjoge1xuICAgICAgICAgICAgZHVyYXRpb246IDUwMDAsXG4gICAgICAgICAgICBpY29uVGhlbWU6IHtcbiAgICAgICAgICAgICAgcHJpbWFyeTogJyNlZjQ0NDQnLFxuICAgICAgICAgICAgICBzZWNvbmRhcnk6ICcjZmZmJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgfX1cbiAgICAgIC8+XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJUb2FzdGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iLCJkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlIiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiIsInN0eWxlIiwiYmFja2dyb3VuZCIsImNvbG9yIiwic3VjY2VzcyIsImljb25UaGVtZSIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/AIInsights.tsx":
/*!*************************************************!*\
  !*** ./src/components/dashboard/AIInsights.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIInsights: () => (/* binding */ AIInsights)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Brain_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AIInsights auto */ \n\n\n// Mock AI analysis data\nconst mockAIInsights = {\n    btc: {\n        symbol: \"BTC\",\n        marketRegime: \"trending_up\",\n        confidence: 78,\n        signal: \"BUY\",\n        explanation: \"Bitcoin is currently in a strong uptrend with increasing volume. The AI has detected a bullish flag pattern forming near $43,000 with RSI showing healthy momentum. Key resistance at $45,000 with potential target at $48,500.\",\n        keyLevels: {\n            support: 41250,\n            resistance: 45000,\n            target: 48500\n        },\n        indicators: [\n            {\n                name: \"RSI\",\n                value: 65,\n                signal: \"BUY\"\n            },\n            {\n                name: \"MACD\",\n                value: 1.2,\n                signal: \"BUY\"\n            },\n            {\n                name: \"Bollinger Bands\",\n                value: 0.8,\n                signal: \"NEUTRAL\"\n            }\n        ]\n    },\n    eth: {\n        symbol: \"ETH\",\n        marketRegime: \"consolidation\",\n        confidence: 62,\n        signal: \"HOLD\",\n        explanation: \"Ethereum is consolidating in a tight range between $2,600-$2,700. The AI suggests waiting for a clear breakout direction. Volume is decreasing, indicating potential for a significant move soon.\",\n        keyLevels: {\n            support: 2600,\n            resistance: 2700,\n            target: 2850\n        },\n        indicators: [\n            {\n                name: \"RSI\",\n                value: 52,\n                signal: \"NEUTRAL\"\n            },\n            {\n                name: \"MACD\",\n                value: -0.1,\n                signal: \"NEUTRAL\"\n            },\n            {\n                name: \"Volume\",\n                value: 0.6,\n                signal: \"SELL\"\n            }\n        ]\n    }\n};\nfunction AIInsights() {\n    const selectedAsset = \"btc\"; // This will come from state management\n    const insights = mockAIInsights[selectedAsset];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"AI Insights\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                children: insights.symbol\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `px-2 py-1 rounded-full text-xs font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getConfidenceColor)(insights.confidence)}`,\n                                children: [\n                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getConfidenceLabel)(insights.confidence),\n                                    \" (\",\n                                    insights.confidence,\n                                    \"%)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                            children: \"Market Regime\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `px-3 py-1 rounded-full text-white text-sm font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMarketRegimeColor)(insights.marketRegime)}`,\n                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMarketRegimeLabel)(insights.marketRegime)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                            children: \"AI Signal\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `px-3 py-1 rounded-full text-white text-sm font-medium ${insights.signal === \"BUY\" ? \"bg-success-500\" : insights.signal === \"SELL\" ? \"bg-danger-500\" : \"bg-gray-500\"}`,\n                            children: insights.signal\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400 mb-3\",\n                        children: \"Key Levels\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400 mb-1\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-success-600\",\n                                        children: [\n                                            \"$\",\n                                            insights.keyLevels.support.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400 mb-1\",\n                                        children: \"Resistance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-danger-600\",\n                                        children: [\n                                            \"$\",\n                                            insights.keyLevels.resistance.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400 mb-1\",\n                                        children: \"Target\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-primary-600\",\n                                        children: [\n                                            \"$\",\n                                            insights.keyLevels.target.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400 mb-3\",\n                        children: \"Key Indicators\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: insights.indicators.map((indicator, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                        children: indicator.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                children: indicator.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-xs px-2 py-1 rounded ${indicator.signal === \"BUY\" ? \"bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200\" : indicator.signal === \"SELL\" ? \"bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200\" : \"bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200\"}`,\n                                                children: indicator.signal\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400 mb-3\",\n                        children: \"AI Analysis\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\",\n                            children: insights.explanation\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/AIInsights.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarketOverview */ \"(ssr)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _AIInsights__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AIInsights */ \"(ssr)/./src/components/dashboard/AIInsights.tsx\");\n/* harmony import */ var _TrendingAssets__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrendingAssets */ \"(ssr)/./src/components/dashboard/TrendingAssets.tsx\");\n/* harmony import */ var _RecentAlerts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RecentAlerts */ \"(ssr)/./src/components/dashboard/RecentAlerts.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \n\n\n\n\n\n\nfunction Dashboard() {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading data\n        const timer = setTimeout(()=>{\n            setIsLoading(false);\n        }, 2000);\n        return ()=>clearTimeout(timer);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n            children: [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__.LoadingCard, {}, i, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_2__.MarketOverview, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIInsights__WEBPACK_IMPORTED_MODULE_3__.AIInsights, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RecentAlerts__WEBPACK_IMPORTED_MODULE_5__.RecentAlerts, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 xl:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrendingAssets__WEBPACK_IMPORTED_MODULE_4__.TrendingAssets, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/Dashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/MarketOverview.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/MarketOverview.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketOverview: () => (/* binding */ MarketOverview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,RefreshCw,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_marketStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/marketStore */ \"(ssr)/./src/stores/marketStore.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ MarketOverview auto */ \n\n\n\n\n\nfunction MarketOverview() {\n    const { marketOverview, currencies, loading, fetchMarketOverview, fetchCurrencies, refreshAllData } = (0,_stores_marketStore__WEBPACK_IMPORTED_MODULE_3__.useMarketStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMarketOverview();\n        fetchCurrencies({\n            limit: 5\n        });\n    }, [\n        fetchMarketOverview,\n        fetchCurrencies\n    ]);\n    const handleRefresh = async ()=>{\n        await refreshAllData();\n    };\n    if (loading.isLoading && !marketOverview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    const topCryptos = currencies.slice(0, 3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                        children: \"Market Overview\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Live\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRefresh,\n                                disabled: loading.isLoading,\n                                className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50\",\n                                title: \"Refresh market data\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: `h-4 w-4 ${loading.isLoading ? \"animate-spin\" : \"\"}`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Market Cap\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(marketOverview.total_market_cap) : \"--\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-sm ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)(marketOverview?.market_cap_change_24h || 0)}`,\n                                        children: [\n                                            marketOverview?.market_cap_change_24h > 0 ? \"+\" : \"\",\n                                            marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(marketOverview.market_cap_change_24h) : \"--\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"24h Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(marketOverview.total_volume) : \"--\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-sm ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)(marketOverview?.volume_change_24h || 0)}`,\n                                        children: [\n                                            marketOverview?.volume_change_24h > 0 ? \"+\" : \"\",\n                                            marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(marketOverview.volume_change_24h) : \"--\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"BTC Dominance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                    children: marketOverview ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(marketOverview.btc_dominance) : \"--\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Fear & Greed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: marketOverview?.fear_greed_index || \"--\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-warning-600\",\n                                        children: marketOverview?.fear_greed_index >= 60 ? \"Greed\" : marketOverview?.fear_greed_index >= 40 ? \"Neutral\" : \"Fear\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"Top Cryptocurrencies\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: topCryptos.length > 0 ? topCryptos.map((crypto)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                children: [\n                                                    crypto.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: crypto.image,\n                                                        alt: crypto.name,\n                                                        className: \"w-6 h-6 rounded-full\",\n                                                        onError: (e)=>{\n                                                            e.currentTarget.style.display = \"none\";\n                                                            e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        style: {\n                                                            display: crypto.image ? \"none\" : \"flex\"\n                                                        },\n                                                        children: crypto.symbol?.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: crypto.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: crypto.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: crypto.current_price ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(crypto.current_price) : \"--\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `text-sm flex items-center ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)(crypto.price_change_percentage_24h || 0)}`,\n                                                children: [\n                                                    (crypto.price_change_percentage_24h || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_RefreshCw_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    (crypto.price_change_percentage_24h || 0) > 0 ? \"+\" : \"\",\n                                                    crypto.price_change_percentage_24h ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(crypto.price_change_percentage_24h) : \"--\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, crypto.symbol, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-4 text-gray-500 dark:text-gray-400\",\n                            children: \"Loading cryptocurrencies...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/MarketOverview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/RecentAlerts.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/RecentAlerts.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentAlerts: () => (/* binding */ RecentAlerts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RecentAlerts auto */ \n\n\n// Mock alerts data\nconst mockAlerts = [\n    {\n        id: \"1\",\n        type: \"pattern_detected\",\n        title: \"Bullish Flag Pattern Detected\",\n        message: \"BTC showing strong bullish flag formation with 78% confidence\",\n        severity: \"high\",\n        symbol: \"BTC\",\n        timestamp: Date.now() - 1800000,\n        isRead: false\n    },\n    {\n        id: \"2\",\n        type: \"regime_change\",\n        title: \"Market Regime Change\",\n        message: \"ETH market regime changed from Ranging to Consolidation\",\n        severity: \"medium\",\n        symbol: \"ETH\",\n        timestamp: Date.now() - 3600000,\n        isRead: false\n    },\n    {\n        id: \"3\",\n        type: \"signal_change\",\n        title: \"AI Signal Update\",\n        message: \"BNB signal changed from HOLD to BUY (confidence: 72%)\",\n        severity: \"medium\",\n        symbol: \"BNB\",\n        timestamp: Date.now() - 7200000,\n        isRead: true\n    },\n    {\n        id: \"4\",\n        type: \"price_level\",\n        title: \"Key Level Approached\",\n        message: \"BTC approaching resistance level at $45,000\",\n        severity: \"low\",\n        symbol: \"BTC\",\n        timestamp: Date.now() - 10800000,\n        isRead: true\n    }\n];\nconst getAlertIcon = (type)=>{\n    switch(type){\n        case \"pattern_detected\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n        case \"regime_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        case \"signal_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        case \"price_level\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        default:\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    }\n};\nconst getSeverityColor = (severity)=>{\n    switch(severity){\n        case \"high\":\n            return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n        case \"medium\":\n            return \"text-warning-600 bg-warning-50 dark:bg-warning-900/20\";\n        case \"low\":\n            return \"text-primary-600 bg-primary-50 dark:bg-primary-900/20\";\n        default:\n            return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n    }\n};\nfunction RecentAlerts() {\n    const unreadCount = mockAlerts.filter((alert)=>!alert.isRead).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Recent Alerts\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: unreadCount\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                        children: \"View All\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: mockAlerts.map((alert)=>{\n                    const IconComponent = getAlertIcon(alert.type);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-4 rounded-lg border transition-colors cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${alert.isRead ? \"border-gray-200 dark:border-gray-600 opacity-75\" : \"border-primary-200 dark:border-primary-700 bg-primary-50/30 dark:bg-primary-900/10\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-2 rounded-full ${getSeverityColor(alert.severity)}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                                    children: alert.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 dark:text-gray-400\",\n                                                            children: alert.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        !alert.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: alert.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatTimeAgo)(alert.timestamp)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs px-2 py-1 rounded-full font-medium ${alert.severity === \"high\" ? \"bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200\" : alert.severity === \"medium\" ? \"bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200\" : \"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"}`,\n                                                    children: alert.severity.toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 15\n                        }, this)\n                    }, alert.id, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            mockAlerts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 dark:text-gray-400\",\n                        children: \"No recent alerts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/RecentAlerts.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/TrendingAssets.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/TrendingAssets.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrendingAssets: () => (/* binding */ TrendingAssets)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _stores_marketStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/stores/marketStore */ \"(ssr)/./src/stores/marketStore.ts\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ TrendingAssets auto */ \n\n\n\n\n\nfunction TrendingAssets() {\n    const { trendingCurrencies, loading, fetchTrendingCurrencies, trackCurrency, untrackCurrency } = (0,_stores_marketStore__WEBPACK_IMPORTED_MODULE_3__.useMarketStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchTrendingCurrencies(10);\n    }, [\n        fetchTrendingCurrencies\n    ]);\n    const handleWatchToggle = async (symbol, isWatched)=>{\n        if (isWatched) {\n            await untrackCurrency(symbol);\n        } else {\n            await trackCurrency(symbol);\n        }\n    };\n    const getSignalColor = (signal)=>{\n        switch(signal){\n            case \"BUY\":\n                return \"text-success-600 bg-success-50 dark:bg-success-900/20\";\n            case \"SELL\":\n                return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n            case \"HOLD\":\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n            default:\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n        }\n    };\n    // Mock AI signals for demonstration (in production, this would come from the AI analysis)\n    const getAISignal = (priceChange)=>{\n        if (priceChange > 5) return {\n            signal: \"BUY\",\n            confidence: 85\n        };\n        if (priceChange > 0) return {\n            signal: \"BUY\",\n            confidence: 65\n        };\n        if (priceChange > -5) return {\n            signal: \"HOLD\",\n            confidence: 50\n        };\n        return {\n            signal: \"SELL\",\n            confidence: 70\n        };\n    };\n    if (loading.isLoading && trendingCurrencies.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                            children: \"Trending Assets\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Trending Assets\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                            children: \"View All\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-b border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"24h Change\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"AI Signal\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: trendingCurrencies.length > 0 ? trendingCurrencies.map((asset, index)=>{\n                                const aiSignal = getAISignal(asset.price_change_percentage_24h || 0);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: [\n                                                            asset.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: asset.image,\n                                                                alt: asset.name,\n                                                                className: \"w-6 h-6 rounded-full\",\n                                                                onError: (e)=>{\n                                                                    e.currentTarget.style.display = \"none\";\n                                                                    e.currentTarget.nextElementSibling.style.display = \"flex\";\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 27\n                                                            }, this) : null,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                style: {\n                                                                    display: asset.image ? \"none\" : \"flex\"\n                                                                },\n                                                                children: asset.symbol?.charAt(0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                children: asset.symbol\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: asset.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: asset.current_price ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatCurrency)(asset.current_price) : \"--\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: asset.market_cap ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(asset.market_cap) : \"--\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-center justify-end space-x-1 ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getPriceChangeColor)(asset.price_change_percentage_24h || 0)}`,\n                                                children: [\n                                                    (asset.price_change_percentage_24h || 0) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            (asset.price_change_percentage_24h || 0) > 0 ? \"+\" : \"\",\n                                                            asset.price_change_percentage_24h ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatPercentage)(asset.price_change_percentage_24h) : \"--\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 dark:text-white\",\n                                                children: asset.total_volume ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatMarketCap)(asset.total_volume) : \"--\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `px-2 py-1 rounded-full text-xs font-medium ${getSignalColor(aiSignal.signal)}`,\n                                                        children: aiSignal.signal\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            aiSignal.confidence,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleWatchToggle(asset.symbol, asset.is_tracked || false),\n                                                        className: `p-1 rounded transition-colors ${asset.is_tracked ? \"text-warning-500 hover:text-warning-600\" : \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"}`,\n                                                        title: asset.is_tracked ? \"Remove from watchlist\" : \"Add to watchlist\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: `h-4 w-4 ${asset.is_tracked ? \"fill-current\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-1 rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\",\n                                                        title: \"View details\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, this);\n                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    colSpan: 6,\n                                    className: \"py-8 text-center text-gray-500 dark:text-gray-400\",\n                                    children: \"No trending assets available\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/TrendingAssets.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\nfunction Header() {\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3); // This will come from store later\n    const toggleTheme = ()=>{\n        setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    className: \"lg:hidden p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center ml-4 lg:ml-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                    children: \"AI Sentinel\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"Adaptive Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleTheme,\n                                    className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    \"aria-label\": \"Toggle theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"relative p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        notificationCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 h-4 w-4 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                            children: notificationCount > 9 ? \"9+\" : notificationCount\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"U\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 bg-black bg-opacity-50\",\n                        onClick: ()=>setIsMobileMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: \"AI Sentinel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"block px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"block px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: \"Analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"block px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: \"Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        current: true\n    },\n    {\n        name: \"Market Analysis\",\n        href: \"/analysis\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        current: false\n    },\n    {\n        name: \"AI Insights\",\n        href: \"/insights\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        current: false\n    },\n    {\n        name: \"Alerts\",\n        href: \"/alerts\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        current: false,\n        badge: 3\n    },\n    {\n        name: \"Watchlist\",\n        href: \"/watchlist\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: \"Performance\",\n        href: \"/performance\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        current: false\n    }\n];\nconst bottomNavigation = [\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Help\",\n        href: \"/help\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 lg:z-50 lg:bg-white lg:dark:bg-gray-800 lg:border-r lg:border-gray-200 lg:dark:border-gray-700 transition-all duration-300\", isCollapsed ? \"lg:w-16\" : \"lg:w-64\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-1 min-h-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsCollapsed(!isCollapsed),\n                                className: \"p-1.5 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 pb-4 space-y-1\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors\", item.current ? \"bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100\" : \"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 h-5 w-5\", item.current ? \"text-primary-600 dark:text-primary-400\" : \"text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 flex-1\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 inline-block py-0.5 px-2 text-xs font-medium bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200 rounded-full\",\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 pb-4 space-y-1 border-t border-gray-200 dark:border-gray-700 pt-4\",\n                            children: bottomNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    className: \"group flex items-center px-2 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"flex-shrink-0 h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-5 gap-1 p-2\",\n                    children: navigation.slice(0, 5).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex flex-col items-center justify-center p-2 text-xs font-medium rounded-md transition-colors\", item.current ? \"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20\" : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"h-5 w-5 mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"truncate w-full text-center\",\n                                    children: item.name.split(\" \")[0]\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -top-1 -right-1 h-4 w-4 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                    children: item.badge > 9 ? \"9+\" : item.badge\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   LoadingTable: () => (/* binding */ LoadingTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nfunction LoadingSpinner({ size = \"md\", className }) {\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-8 h-8\",\n        lg: \"w-12 h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600\", sizeClasses[size], className)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-200 dark:bg-gray-700 rounded\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingTable() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                [\n                    ...Array(5)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-16\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this)\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alertsApi: () => (/* binding */ alertsApi),\n/* harmony export */   analysisApi: () => (/* binding */ analysisApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   healthApi: () => (/* binding */ healthApi),\n/* harmony export */   marketApi: () => (/* binding */ marketApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../node_modules/axios/lib/axios.js\");\n/**\n * API client for the Adaptive AI Sentinel backend\n */ \nconst API_BASE_URL = \"http://localhost:8000\" || 0;\n// Create axios instance with default config\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: `${API_BASE_URL}/api/v1`,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor for logging\napiClient.interceptors.request.use((config)=>{\n    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    return config;\n}, (error)=>{\n    console.error(\"API Request Error:\", error);\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    console.error(\"API Response Error:\", error.response?.data || error.message);\n    return Promise.reject(error);\n});\n// Market Data API\nconst marketApi = {\n    // Get list of cryptocurrencies\n    getCryptocurrencies: async (params)=>{\n        const response = await apiClient.get(\"/market/cryptocurrencies\", {\n            params\n        });\n        return response.data.data;\n    },\n    // Get specific cryptocurrency\n    getCryptocurrency: async (symbol)=>{\n        const response = await apiClient.get(`/market/cryptocurrencies/${symbol}`);\n        return response.data.data;\n    },\n    // Get price history\n    getPriceHistory: async (symbol, timeframe = \"1d\", days = 30)=>{\n        const response = await apiClient.get(`/market/cryptocurrencies/${symbol}/price-history`, {\n            params: {\n                timeframe,\n                days\n            }\n        });\n        return response.data.data;\n    },\n    // Get market overview\n    getMarketOverview: async ()=>{\n        const response = await apiClient.get(\"/market/market-overview\");\n        return response.data.data;\n    },\n    // Get trending cryptocurrencies\n    getTrending: async (limit = 10)=>{\n        const response = await apiClient.get(\"/market/trending\", {\n            params: {\n                limit\n            }\n        });\n        return response.data.data;\n    },\n    // Track cryptocurrency\n    trackCryptocurrency: async (symbol)=>{\n        const response = await apiClient.post(`/market/cryptocurrencies/${symbol}/track`);\n        return response.data;\n    },\n    // Untrack cryptocurrency\n    untrackCryptocurrency: async (symbol)=>{\n        const response = await apiClient.delete(`/market/cryptocurrencies/${symbol}/track`);\n        return response.data;\n    },\n    // Refresh market data\n    refreshMarketData: async ()=>{\n        const response = await apiClient.post(\"/market/refresh\");\n        return response.data;\n    }\n};\n// AI Analysis API\nconst analysisApi = {\n    // Get AI insights for a cryptocurrency\n    getAIInsights: async (symbol)=>{\n        const response = await apiClient.get(`/analysis/ai-insights/${symbol}`);\n        return response.data.data;\n    },\n    // Get market regime\n    getMarketRegime: async (symbol)=>{\n        const response = await apiClient.get(`/analysis/market-regime/${symbol}`);\n        return response.data.data;\n    },\n    // Get technical indicators\n    getTechnicalIndicators: async (symbol, indicators)=>{\n        const response = await apiClient.get(`/analysis/technical-indicators/${symbol}`, {\n            params: indicators ? {\n                indicators\n            } : {}\n        });\n        return response.data.data;\n    },\n    // Get chart patterns\n    getChartPatterns: async (symbol)=>{\n        const response = await apiClient.get(`/analysis/chart-patterns/${symbol}`);\n        return response.data.data;\n    }\n};\n// Alerts API\nconst alertsApi = {\n    // Get alerts\n    getAlerts: async (params)=>{\n        const response = await apiClient.get(\"/alerts\", {\n            params\n        });\n        return {\n            alerts: response.data.data,\n            total: response.data.total,\n            unread_count: response.data.unread_count\n        };\n    },\n    // Get specific alert\n    getAlert: async (alertId)=>{\n        const response = await apiClient.get(`/alerts/${alertId}`);\n        return response.data.data;\n    },\n    // Mark alert as read\n    markAsRead: async (alertId)=>{\n        const response = await apiClient.patch(`/alerts/${alertId}/read`);\n        return response.data;\n    },\n    // Mark all alerts as read\n    markAllAsRead: async ()=>{\n        const response = await apiClient.patch(\"/alerts/mark-all-read\");\n        return response.data;\n    },\n    // Delete alert\n    deleteAlert: async (alertId)=>{\n        const response = await apiClient.delete(`/alerts/${alertId}`);\n        return response.data;\n    },\n    // Get alert statistics\n    getAlertStats: async ()=>{\n        const response = await apiClient.get(\"/alerts/stats/summary\");\n        return response.data.data;\n    }\n};\n// Health API\nconst healthApi = {\n    // Basic health check\n    getHealth: async ()=>{\n        const response = await apiClient.get(\"/health\");\n        return response.data;\n    },\n    // Detailed health check\n    getDetailedHealth: async ()=>{\n        const response = await apiClient.get(\"/health/detailed\");\n        return response.data;\n    }\n};\n// Export default API client for custom requests\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatMarketCap: () => (/* binding */ formatMarketCap),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatTimeAgo: () => (/* binding */ formatTimeAgo),\n/* harmony export */   formatVolume: () => (/* binding */ formatVolume),\n/* harmony export */   getConfidenceColor: () => (/* binding */ getConfidenceColor),\n/* harmony export */   getConfidenceLabel: () => (/* binding */ getConfidenceLabel),\n/* harmony export */   getMarketRegimeColor: () => (/* binding */ getMarketRegimeColor),\n/* harmony export */   getMarketRegimeLabel: () => (/* binding */ getMarketRegimeLabel),\n/* harmony export */   getPriceChangeColor: () => (/* binding */ getPriceChangeColor),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(value, currency = \"USD\", minimumFractionDigits = 2, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value);\n}\nfunction formatNumber(value, minimumFractionDigits = 0, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value);\n}\nfunction formatPercentage(value, minimumFractionDigits = 2, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"percent\",\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value / 100);\n}\nfunction formatMarketCap(value) {\n    if (value >= 1e12) {\n        return `$${(value / 1e12).toFixed(2)}T`;\n    } else if (value >= 1e9) {\n        return `$${(value / 1e9).toFixed(2)}B`;\n    } else if (value >= 1e6) {\n        return `$${(value / 1e6).toFixed(2)}M`;\n    } else if (value >= 1e3) {\n        return `$${(value / 1e3).toFixed(2)}K`;\n    }\n    return formatCurrency(value);\n}\nfunction formatVolume(value) {\n    return formatMarketCap(value);\n}\nfunction formatTimeAgo(timestamp) {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    if (days > 0) {\n        return `${days}d ago`;\n    } else if (hours > 0) {\n        return `${hours}h ago`;\n    } else if (minutes > 0) {\n        return `${minutes}m ago`;\n    } else {\n        return `${seconds}s ago`;\n    }\n}\nfunction getPriceChangeColor(change) {\n    if (change > 0) return \"text-success-600\";\n    if (change < 0) return \"text-danger-600\";\n    return \"text-gray-600\";\n}\nfunction getConfidenceColor(confidence) {\n    if (confidence >= 80) return \"text-success-600 bg-success-50\";\n    if (confidence >= 60) return \"text-warning-600 bg-warning-50\";\n    return \"text-danger-600 bg-danger-50\";\n}\nfunction getConfidenceLabel(confidence) {\n    if (confidence >= 80) return \"High\";\n    if (confidence >= 60) return \"Medium\";\n    return \"Low\";\n}\nfunction getMarketRegimeColor(regime) {\n    switch(regime.toLowerCase()){\n        case \"trending_up\":\n        case \"breakout\":\n            return \"bg-success-500\";\n        case \"trending_down\":\n            return \"bg-danger-500\";\n        case \"ranging\":\n        case \"consolidation\":\n            return \"bg-primary-500\";\n        case \"volatile\":\n            return \"bg-warning-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n}\nfunction getMarketRegimeLabel(regime) {\n    switch(regime.toLowerCase()){\n        case \"trending_up\":\n            return \"Trending Up\";\n        case \"trending_down\":\n            return \"Trending Down\";\n        case \"ranging\":\n            return \"Ranging\";\n        case \"volatile\":\n            return \"Volatile\";\n        case \"consolidation\":\n            return \"Consolidation\";\n        case \"breakout\":\n            return \"Breakout\";\n        default:\n            return \"Unknown\";\n    }\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/stores/marketStore.ts":
/*!***********************************!*\
  !*** ./src/stores/marketStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMarketStore: () => (/* binding */ useMarketStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand */ \"(ssr)/../node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/../node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../node_modules/react-hot-toast/dist/index.mjs\");\n/**\n * Market data store using Zustand\n */ \n\n\n\nconst useMarketStore = (0,zustand__WEBPACK_IMPORTED_MODULE_2__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_3__.devtools)((set, get)=>({\n        // Initial state\n        currencies: [],\n        selectedCurrency: \"BTC\",\n        marketOverview: null,\n        priceData: {},\n        aiAnalysis: {},\n        trendingCurrencies: [],\n        loading: {\n            isLoading: false,\n            error: undefined\n        },\n        // Actions\n        fetchCurrencies: async (params)=>{\n            set((state)=>({\n                    loading: {\n                        ...state.loading,\n                        isLoading: true,\n                        error: undefined\n                    }\n                }));\n            try {\n                const currencies = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.marketApi.getCryptocurrencies(params);\n                set({\n                    currencies\n                });\n                // If no currency is selected, select the first one\n                if (!get().selectedCurrency && currencies.length > 0) {\n                    set({\n                        selectedCurrency: currencies[0].symbol\n                    });\n                }\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || error.message || \"Failed to fetch cryptocurrencies\";\n                set((state)=>({\n                        loading: {\n                            ...state.loading,\n                            error: errorMessage\n                        }\n                    }));\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n            } finally{\n                set((state)=>({\n                        loading: {\n                            ...state.loading,\n                            isLoading: false\n                        }\n                    }));\n            }\n        },\n        fetchMarketOverview: async ()=>{\n            try {\n                const overview = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.marketApi.getMarketOverview();\n                set({\n                    marketOverview: overview\n                });\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || error.message || \"Failed to fetch market overview\";\n                console.error(\"Market overview error:\", errorMessage);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n            }\n        },\n        fetchPriceData: async (symbol, timeframe = \"1d\", days = 30)=>{\n            try {\n                const data = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.marketApi.getPriceHistory(symbol, timeframe, days);\n                set((state)=>({\n                        priceData: {\n                            ...state.priceData,\n                            [symbol]: data\n                        }\n                    }));\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || error.message || `Failed to fetch price data for ${symbol}`;\n                console.error(\"Price data error:\", errorMessage);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n            }\n        },\n        fetchAIAnalysis: async (symbol)=>{\n            try {\n                const analysis = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.analysisApi.getAIInsights(symbol);\n                set((state)=>({\n                        aiAnalysis: {\n                            ...state.aiAnalysis,\n                            [symbol]: analysis\n                        }\n                    }));\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || error.message || `Failed to fetch AI analysis for ${symbol}`;\n                console.error(\"AI analysis error:\", errorMessage);\n            // Don't show toast for AI analysis errors as they might be expected for some currencies\n            }\n        },\n        fetchTrendingCurrencies: async (limit = 10)=>{\n            try {\n                const trending = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.marketApi.getTrending(limit);\n                set({\n                    trendingCurrencies: trending\n                });\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || error.message || \"Failed to fetch trending cryptocurrencies\";\n                console.error(\"Trending currencies error:\", errorMessage);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n            }\n        },\n        setSelectedCurrency: (symbol)=>{\n            set({\n                selectedCurrency: symbol\n            });\n            // Automatically fetch AI analysis for the selected currency\n            get().fetchAIAnalysis(symbol);\n            get().fetchPriceData(symbol);\n        },\n        trackCurrency: async (symbol)=>{\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.marketApi.trackCryptocurrency(symbol);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].success(`${symbol} added to watchlist`);\n                // Update the currency in the list\n                set((state)=>({\n                        currencies: state.currencies.map((currency)=>currency.symbol === symbol ? {\n                                ...currency,\n                                is_tracked: true\n                            } : currency)\n                    }));\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || error.message || `Failed to track ${symbol}`;\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n            }\n        },\n        untrackCurrency: async (symbol)=>{\n            try {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.marketApi.untrackCryptocurrency(symbol);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].success(`${symbol} removed from watchlist`);\n                // Update the currency in the list\n                set((state)=>({\n                        currencies: state.currencies.map((currency)=>currency.symbol === symbol ? {\n                                ...currency,\n                                is_tracked: false\n                            } : currency)\n                    }));\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || error.message || `Failed to untrack ${symbol}`;\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n            }\n        },\n        refreshAllData: async ()=>{\n            set((state)=>({\n                    loading: {\n                        ...state.loading,\n                        isLoading: true,\n                        error: undefined\n                    }\n                }));\n            try {\n                // Refresh market data on backend\n                await _lib_api__WEBPACK_IMPORTED_MODULE_0__.marketApi.refreshMarketData();\n                // Fetch all data\n                await Promise.all([\n                    get().fetchCurrencies(),\n                    get().fetchMarketOverview(),\n                    get().fetchTrendingCurrencies()\n                ]);\n                // Fetch data for selected currency\n                const selectedCurrency = get().selectedCurrency;\n                if (selectedCurrency) {\n                    await Promise.all([\n                        get().fetchPriceData(selectedCurrency),\n                        get().fetchAIAnalysis(selectedCurrency)\n                    ]);\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].success(\"Market data refreshed successfully\");\n            } catch (error) {\n                const errorMessage = error.response?.data?.error || error.message || \"Failed to refresh market data\";\n                set((state)=>({\n                        loading: {\n                            ...state.loading,\n                            error: errorMessage\n                        }\n                    }));\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n            } finally{\n                set((state)=>({\n                        loading: {\n                            ...state.loading,\n                            isLoading: false\n                        }\n                    }));\n            }\n        },\n        clearError: ()=>{\n            set((state)=>({\n                    loading: {\n                        ...state.loading,\n                        error: undefined\n                    }\n                }));\n        }\n    }), {\n    name: \"market-store\"\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/stores/marketStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c405cee33148\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VudGluZWxhLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83ZDgxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzQwNWNlZTMzMTQ4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Adaptive AI Sentinel - Cryptocurrency Analysis Platform\",\n    description: \"AI-powered cryptocurrency analysis platform with market regime detection and intelligent trading signals\",\n    keywords: [\n        \"cryptocurrency\",\n        \"trading\",\n        \"AI\",\n        \"technical analysis\",\n        \"market analysis\"\n    ],\n    authors: [\n        {\n            name: \"Adaptive AI Sentinel Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#0ea5e9\",\n    openGraph: {\n        title: \"Adaptive AI Sentinel\",\n        description: \"AI-powered cryptocurrency analysis platform\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Adaptive AI Sentinel\",\n        description: \"AI-powered cryptocurrency analysis platform\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/layout.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/layout.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/Dashboard */ \"(rsc)/./src/components/dashboard/Dashboard.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Sidebar */ \"(rsc)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(rsc)/./src/components/ui/LoadingSpinner.tsx\");\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__.Header, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 lg:ml-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                            children: \"AI Sentinel Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600 dark:text-gray-400\",\n                                            children: \"Real-time cryptocurrency analysis with AI-powered market regime detection\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__.LoadingSpinner, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_2__.Dashboard, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Dashboard: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx#Dashboard`);


/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx#Header`);


/***/ }),

/***/ "(rsc)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sidebar: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx#Sidebar`);


/***/ }),

/***/ "(rsc)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   LoadingTable: () => (/* binding */ LoadingTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nfunction LoadingSpinner({ size = \"md\", className }) {\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-8 h-8\",\n        lg: \"w-12 h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600\", sizeClasses[size], className)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-200 dark:bg-gray-700 rounded\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingTable() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                [\n                    ...Array(5)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-16\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this)\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatMarketCap: () => (/* binding */ formatMarketCap),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatTimeAgo: () => (/* binding */ formatTimeAgo),\n/* harmony export */   formatVolume: () => (/* binding */ formatVolume),\n/* harmony export */   getConfidenceColor: () => (/* binding */ getConfidenceColor),\n/* harmony export */   getConfidenceLabel: () => (/* binding */ getConfidenceLabel),\n/* harmony export */   getMarketRegimeColor: () => (/* binding */ getMarketRegimeColor),\n/* harmony export */   getMarketRegimeLabel: () => (/* binding */ getMarketRegimeLabel),\n/* harmony export */   getPriceChangeColor: () => (/* binding */ getPriceChangeColor),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(value, currency = \"USD\", minimumFractionDigits = 2, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value);\n}\nfunction formatNumber(value, minimumFractionDigits = 0, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value);\n}\nfunction formatPercentage(value, minimumFractionDigits = 2, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"percent\",\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value / 100);\n}\nfunction formatMarketCap(value) {\n    if (value >= 1e12) {\n        return `$${(value / 1e12).toFixed(2)}T`;\n    } else if (value >= 1e9) {\n        return `$${(value / 1e9).toFixed(2)}B`;\n    } else if (value >= 1e6) {\n        return `$${(value / 1e6).toFixed(2)}M`;\n    } else if (value >= 1e3) {\n        return `$${(value / 1e3).toFixed(2)}K`;\n    }\n    return formatCurrency(value);\n}\nfunction formatVolume(value) {\n    return formatMarketCap(value);\n}\nfunction formatTimeAgo(timestamp) {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    if (days > 0) {\n        return `${days}d ago`;\n    } else if (hours > 0) {\n        return `${hours}h ago`;\n    } else if (minutes > 0) {\n        return `${minutes}m ago`;\n    } else {\n        return `${seconds}s ago`;\n    }\n}\nfunction getPriceChangeColor(change) {\n    if (change > 0) return \"text-success-600\";\n    if (change < 0) return \"text-danger-600\";\n    return \"text-gray-600\";\n}\nfunction getConfidenceColor(confidence) {\n    if (confidence >= 80) return \"text-success-600 bg-success-50\";\n    if (confidence >= 60) return \"text-warning-600 bg-warning-50\";\n    return \"text-danger-600 bg-danger-50\";\n}\nfunction getConfidenceLabel(confidence) {\n    if (confidence >= 80) return \"High\";\n    if (confidence >= 60) return \"Medium\";\n    return \"Low\";\n}\nfunction getMarketRegimeColor(regime) {\n    switch(regime.toLowerCase()){\n        case \"trending_up\":\n        case \"breakout\":\n            return \"bg-success-500\";\n        case \"trending_down\":\n            return \"bg-danger-500\";\n        case \"ranging\":\n        case \"consolidation\":\n            return \"bg-primary-500\";\n        case \"volatile\":\n            return \"bg-warning-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n}\nfunction getMarketRegimeLabel(regime) {\n    switch(regime.toLowerCase()){\n        case \"trending_up\":\n            return \"Trending Up\";\n        case \"trending_down\":\n            return \"Trending Down\";\n        case \"ranging\":\n            return \"Ranging\";\n        case \"volatile\":\n            return \"Volatile\";\n        case \"consolidation\":\n            return \"Consolidation\";\n        case \"breakout\":\n            return \"Breakout\";\n        default:\n            return \"Unknown\";\n    }\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/lucide-react","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/use-sync-external-store","vendor-chunks/debug","vendor-chunks/zustand","vendor-chunks/get-proto","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();