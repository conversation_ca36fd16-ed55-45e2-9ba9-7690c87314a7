/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?3a46\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRnNlbnRpbmVsYS1pYSUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGcHJvdmlkZXJzLnRzeCZtb2R1bGVzPSUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRnNlbnRpbmVsYS1pYSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRnNlbnRpbmVsYS1pYSUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VudGluZWxhLWZyb250ZW5kLz9hMGU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZXhhbmRyZXNpbWFzbWFjaWVsL0RvY3VtZW50cy9pYS1zaXN0ZW1hcy9zZW50aW5lbGEtaWEvZnJvbnRlbmQvc3JjL2FwcC9wcm92aWRlcnMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fproviders.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FSidebar.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FSidebar.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/Dashboard.tsx */ \"(ssr)/./src/components/dashboard/Dashboard.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Sidebar.tsx */ \"(ssr)/./src/components/layout/Sidebar.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRnNlbnRpbmVsYS1pYSUyRmZyb250ZW5kJTJGc3JjJTJGY29tcG9uZW50cyUyRmRhc2hib2FyZCUyRkRhc2hib2FyZC50c3gmbW9kdWxlcz0lMkZVc2VycyUyRmFsZXhhbmRyZXNpbWFzbWFjaWVsJTJGRG9jdW1lbnRzJTJGaWEtc2lzdGVtYXMlMkZzZW50aW5lbGEtaWElMkZmcm9udGVuZCUyRnNyYyUyRmNvbXBvbmVudHMlMkZsYXlvdXQlMkZIZWFkZXIudHN4Jm1vZHVsZXM9JTJGVXNlcnMlMkZhbGV4YW5kcmVzaW1hc21hY2llbCUyRkRvY3VtZW50cyUyRmlhLXNpc3RlbWFzJTJGc2VudGluZWxhLWlhJTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGU2lkZWJhci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFtSjtBQUNuSixnTEFBNkk7QUFDN0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW50aW5lbGEtZnJvbnRlbmQvPzVhM2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxleGFuZHJlc2ltYXNtYWNpZWwvRG9jdW1lbnRzL2lhLXNpc3RlbWFzL3NlbnRpbmVsYS1pYS9mcm9udGVuZC9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsZXhhbmRyZXNpbWFzbWFjaWVsL0RvY3VtZW50cy9pYS1zaXN0ZW1hcy9zZW50aW5lbGEtaWEvZnJvbnRlbmQvc3JjL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGV4YW5kcmVzaW1hc21hY2llbC9Eb2N1bWVudHMvaWEtc2lzdGVtYXMvc2VudGluZWxhLWlhL2Zyb250ZW5kL3NyYy9jb21wb25lbnRzL2xheW91dC9TaWRlYmFyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2FSidebar.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"system\",\n        enableSystem: true,\n        disableTransitionOnChange: true,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#22c55e\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/providers.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/providers.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/AIInsights.tsx":
/*!*************************************************!*\
  !*** ./src/components/dashboard/AIInsights.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIInsights: () => (/* binding */ AIInsights)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Brain_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AIInsights auto */ \n\n\n// Mock AI analysis data\nconst mockAIInsights = {\n    btc: {\n        symbol: \"BTC\",\n        marketRegime: \"trending_up\",\n        confidence: 78,\n        signal: \"BUY\",\n        explanation: \"Bitcoin is currently in a strong uptrend with increasing volume. The AI has detected a bullish flag pattern forming near $43,000 with RSI showing healthy momentum. Key resistance at $45,000 with potential target at $48,500.\",\n        keyLevels: {\n            support: 41250,\n            resistance: 45000,\n            target: 48500\n        },\n        indicators: [\n            {\n                name: \"RSI\",\n                value: 65,\n                signal: \"BUY\"\n            },\n            {\n                name: \"MACD\",\n                value: 1.2,\n                signal: \"BUY\"\n            },\n            {\n                name: \"Bollinger Bands\",\n                value: 0.8,\n                signal: \"NEUTRAL\"\n            }\n        ]\n    },\n    eth: {\n        symbol: \"ETH\",\n        marketRegime: \"consolidation\",\n        confidence: 62,\n        signal: \"HOLD\",\n        explanation: \"Ethereum is consolidating in a tight range between $2,600-$2,700. The AI suggests waiting for a clear breakout direction. Volume is decreasing, indicating potential for a significant move soon.\",\n        keyLevels: {\n            support: 2600,\n            resistance: 2700,\n            target: 2850\n        },\n        indicators: [\n            {\n                name: \"RSI\",\n                value: 52,\n                signal: \"NEUTRAL\"\n            },\n            {\n                name: \"MACD\",\n                value: -0.1,\n                signal: \"NEUTRAL\"\n            },\n            {\n                name: \"Volume\",\n                value: 0.6,\n                signal: \"SELL\"\n            }\n        ]\n    }\n};\nfunction AIInsights() {\n    const selectedAsset = \"btc\"; // This will come from state management\n    const insights = mockAIInsights[selectedAsset];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"AI Insights\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                children: insights.symbol\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `px-2 py-1 rounded-full text-xs font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getConfidenceColor)(insights.confidence)}`,\n                                children: [\n                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getConfidenceLabel)(insights.confidence),\n                                    \" (\",\n                                    insights.confidence,\n                                    \"%)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                            children: \"Market Regime\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `px-3 py-1 rounded-full text-white text-sm font-medium ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMarketRegimeColor)(insights.marketRegime)}`,\n                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getMarketRegimeLabel)(insights.marketRegime)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                            children: \"AI Signal\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `px-3 py-1 rounded-full text-white text-sm font-medium ${insights.signal === \"BUY\" ? \"bg-success-500\" : insights.signal === \"SELL\" ? \"bg-danger-500\" : \"bg-gray-500\"}`,\n                            children: insights.signal\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400 mb-3\",\n                        children: \"Key Levels\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400 mb-1\",\n                                        children: \"Support\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-success-600\",\n                                        children: [\n                                            \"$\",\n                                            insights.keyLevels.support.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400 mb-1\",\n                                        children: \"Resistance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-danger-600\",\n                                        children: [\n                                            \"$\",\n                                            insights.keyLevels.resistance.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400 mb-1\",\n                                        children: \"Target\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-primary-600\",\n                                        children: [\n                                            \"$\",\n                                            insights.keyLevels.target.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400 mb-3\",\n                        children: \"Key Indicators\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: insights.indicators.map((indicator, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                        children: indicator.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                children: indicator.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-xs px-2 py-1 rounded ${indicator.signal === \"BUY\" ? \"bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200\" : indicator.signal === \"SELL\" ? \"bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200\" : \"bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200\"}`,\n                                                children: indicator.signal\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-600 dark:text-gray-400 mb-3\",\n                        children: \"AI Analysis\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-700 dark:text-gray-300 leading-relaxed\",\n                            children: insights.explanation\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/AIInsights.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/AIInsights.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarketOverview */ \"(ssr)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _AIInsights__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AIInsights */ \"(ssr)/./src/components/dashboard/AIInsights.tsx\");\n/* harmony import */ var _TrendingAssets__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TrendingAssets */ \"(ssr)/./src/components/dashboard/TrendingAssets.tsx\");\n/* harmony import */ var _RecentAlerts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RecentAlerts */ \"(ssr)/./src/components/dashboard/RecentAlerts.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(ssr)/./src/components/ui/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \n\n\n\n\n\n\nfunction Dashboard() {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading data\n        const timer = setTimeout(()=>{\n            setIsLoading(false);\n        }, 2000);\n        return ()=>clearTimeout(timer);\n    }, []);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n            children: [\n                ...Array(6)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_6__.LoadingCard, {}, i, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_2__.MarketOverview, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIInsights__WEBPACK_IMPORTED_MODULE_3__.AIInsights, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RecentAlerts__WEBPACK_IMPORTED_MODULE_5__.RecentAlerts, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 xl:col-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrendingAssets__WEBPACK_IMPORTED_MODULE_4__.TrendingAssets, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNNO0FBQ1I7QUFDUTtBQUNKO0FBQ2U7QUFFdEQsU0FBU087SUFDZCxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR1QsK0NBQVFBLENBQUM7SUFFM0NDLGdEQUFTQSxDQUFDO1FBQ1Isd0JBQXdCO1FBQ3hCLE1BQU1TLFFBQVFDLFdBQVc7WUFDdkJGLGFBQWE7UUFDZixHQUFHO1FBRUgsT0FBTyxJQUFNRyxhQUFhRjtJQUM1QixHQUFHLEVBQUU7SUFFTCxJQUFJRixXQUFXO1FBQ2IscUJBQ0UsOERBQUNLO1lBQUlDLFdBQVU7c0JBQ1o7bUJBQUlDLE1BQU07YUFBRyxDQUFDQyxHQUFHLENBQUMsQ0FBQ0MsR0FBR0Msa0JBQ3JCLDhEQUFDWixzRUFBV0EsTUFBTVk7Ozs7Ozs7Ozs7SUFJMUI7SUFFQSxxQkFDRSw4REFBQ0w7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNaLDJEQUFjQTs7Ozs7MEJBR2YsOERBQUNXO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNYLG1EQUFVQTs7Ozs7Ozs7OztrQ0FJYiw4REFBQ1U7a0NBQ0MsNEVBQUNSLHVEQUFZQTs7Ozs7Ozs7OztrQ0FJZiw4REFBQ1E7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNWLDJEQUFjQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUt6QiIsInNvdXJjZXMiOlsid2VicGFjazovL3NlbnRpbmVsYS1mcm9udGVuZC8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9EYXNoYm9hcmQudHN4P2E5OTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTWFya2V0T3ZlcnZpZXcgfSBmcm9tICcuL01hcmtldE92ZXJ2aWV3JztcbmltcG9ydCB7IEFJSW5zaWdodHMgfSBmcm9tICcuL0FJSW5zaWdodHMnO1xuaW1wb3J0IHsgVHJlbmRpbmdBc3NldHMgfSBmcm9tICcuL1RyZW5kaW5nQXNzZXRzJztcbmltcG9ydCB7IFJlY2VudEFsZXJ0cyB9IGZyb20gJy4vUmVjZW50QWxlcnRzJztcbmltcG9ydCB7IExvYWRpbmdDYXJkIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0xvYWRpbmdTcGlubmVyJztcblxuZXhwb3J0IGZ1bmN0aW9uIERhc2hib2FyZCgpIHtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gU2ltdWxhdGUgbG9hZGluZyBkYXRhXG4gICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfSwgMjAwMCk7XG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgfSwgW10pO1xuXG4gIGlmIChpc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIHhsOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgIHtbLi4uQXJyYXkoNildLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgIDxMb2FkaW5nQ2FyZCBrZXk9e2l9IC8+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBNYXJrZXQgT3ZlcnZpZXcgKi99XG4gICAgICA8TWFya2V0T3ZlcnZpZXcgLz5cblxuICAgICAgey8qIE1haW4gR3JpZCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiB4bDpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICB7LyogQUkgSW5zaWdodHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwieGw6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgIDxBSUluc2lnaHRzIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSZWNlbnQgQWxlcnRzICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxSZWNlbnRBbGVydHMgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFRyZW5kaW5nIEFzc2V0cyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yIHhsOmNvbC1zcGFuLTNcIj5cbiAgICAgICAgICA8VHJlbmRpbmdBc3NldHMgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIk1hcmtldE92ZXJ2aWV3IiwiQUlJbnNpZ2h0cyIsIlRyZW5kaW5nQXNzZXRzIiwiUmVjZW50QWxlcnRzIiwiTG9hZGluZ0NhcmQiLCJEYXNoYm9hcmQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ0aW1lciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJBcnJheSIsIm1hcCIsIl8iLCJpIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/Dashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/MarketOverview.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/MarketOverview.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketOverview: () => (/* binding */ MarketOverview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarketOverview auto */ \n\n\n// Mock data - will be replaced with real API data\nconst mockMarketData = {\n    totalMarketCap: 1750000000000,\n    totalVolume: 85000000000,\n    btcDominance: 52.3,\n    marketCapChange24h: 2.4,\n    volumeChange24h: -5.2,\n    fearGreedIndex: 68\n};\nconst topCryptos = [\n    {\n        symbol: \"BTC\",\n        name: \"Bitcoin\",\n        price: 43250.50,\n        change24h: 2.4,\n        marketCap: 847000000000\n    },\n    {\n        symbol: \"ETH\",\n        name: \"Ethereum\",\n        price: 2650.75,\n        change24h: -1.2,\n        marketCap: 318000000000\n    },\n    {\n        symbol: \"BNB\",\n        name: \"BNB\",\n        price: 315.20,\n        change24h: 0.8,\n        marketCap: 47000000000\n    }\n];\nfunction MarketOverview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                        children: \"Market Overview\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Live\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Market Cap\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency)(mockMarketData.totalMarketCap, \"USD\", 0, 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-sm ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getPriceChangeColor)(mockMarketData.marketCapChange24h)}`,\n                                        children: [\n                                            mockMarketData.marketCapChange24h > 0 ? \"+\" : \"\",\n                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatPercentage)(mockMarketData.marketCapChange24h)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"24h Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency)(mockMarketData.totalVolume, \"USD\", 0, 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-sm ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getPriceChangeColor)(mockMarketData.volumeChange24h)}`,\n                                        children: [\n                                            mockMarketData.volumeChange24h > 0 ? \"+\" : \"\",\n                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatPercentage)(mockMarketData.volumeChange24h)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"BTC Dominance\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatPercentage)(mockMarketData.btcDominance)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-600 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Fear & Greed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: mockMarketData.fearGreedIndex\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-warning-600\",\n                                        children: \"Greed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"Top Cryptocurrencies\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: topCryptos.map((crypto)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-white\",\n                                                    children: crypto.symbol.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: crypto.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: crypto.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency)(crypto.price)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `text-sm flex items-center ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getPriceChangeColor)(crypto.change24h)}`,\n                                                children: [\n                                                    crypto.change24h > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-3 w-3 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    crypto.change24h > 0 ? \"+\" : \"\",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatPercentage)(crypto.change24h)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, crypto.symbol, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/MarketOverview.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/MarketOverview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/RecentAlerts.tsx":
/*!***************************************************!*\
  !*** ./src/components/dashboard/RecentAlerts.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentAlerts: () => (/* binding */ RecentAlerts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Clock,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ RecentAlerts auto */ \n\n\n// Mock alerts data\nconst mockAlerts = [\n    {\n        id: \"1\",\n        type: \"pattern_detected\",\n        title: \"Bullish Flag Pattern Detected\",\n        message: \"BTC showing strong bullish flag formation with 78% confidence\",\n        severity: \"high\",\n        symbol: \"BTC\",\n        timestamp: Date.now() - 1800000,\n        isRead: false\n    },\n    {\n        id: \"2\",\n        type: \"regime_change\",\n        title: \"Market Regime Change\",\n        message: \"ETH market regime changed from Ranging to Consolidation\",\n        severity: \"medium\",\n        symbol: \"ETH\",\n        timestamp: Date.now() - 3600000,\n        isRead: false\n    },\n    {\n        id: \"3\",\n        type: \"signal_change\",\n        title: \"AI Signal Update\",\n        message: \"BNB signal changed from HOLD to BUY (confidence: 72%)\",\n        severity: \"medium\",\n        symbol: \"BNB\",\n        timestamp: Date.now() - 7200000,\n        isRead: true\n    },\n    {\n        id: \"4\",\n        type: \"price_level\",\n        title: \"Key Level Approached\",\n        message: \"BTC approaching resistance level at $45,000\",\n        severity: \"low\",\n        symbol: \"BTC\",\n        timestamp: Date.now() - 10800000,\n        isRead: true\n    }\n];\nconst getAlertIcon = (type)=>{\n    switch(type){\n        case \"pattern_detected\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n        case \"regime_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        case \"signal_change\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        case \"price_level\":\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        default:\n            return _barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n    }\n};\nconst getSeverityColor = (severity)=>{\n    switch(severity){\n        case \"high\":\n            return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n        case \"medium\":\n            return \"text-warning-600 bg-warning-50 dark:bg-warning-900/20\";\n        case \"low\":\n            return \"text-primary-600 bg-primary-50 dark:bg-primary-900/20\";\n        default:\n            return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n    }\n};\nfunction RecentAlerts() {\n    const unreadCount = mockAlerts.filter((alert)=>!alert.isRead).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Recent Alerts\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                children: unreadCount\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                        children: \"View All\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: mockAlerts.map((alert)=>{\n                    const IconComponent = getAlertIcon(alert.type);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-4 rounded-lg border transition-colors cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${alert.isRead ? \"border-gray-200 dark:border-gray-600 opacity-75\" : \"border-primary-200 dark:border-primary-700 bg-primary-50/30 dark:bg-primary-900/10\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-2 rounded-full ${getSeverityColor(alert.severity)}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                                    children: alert.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-gray-500 dark:text-gray-400\",\n                                                            children: alert.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        !alert.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-primary-500 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: alert.message\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatTimeAgo)(alert.timestamp)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs px-2 py-1 rounded-full font-medium ${alert.severity === \"high\" ? \"bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200\" : alert.severity === \"medium\" ? \"bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200\" : \"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"}`,\n                                                    children: alert.severity.toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 15\n                        }, this)\n                    }, alert.id, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            mockAlerts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Clock_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 dark:text-gray-400\",\n                        children: \"No recent alerts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/RecentAlerts.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/RecentAlerts.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/TrendingAssets.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/TrendingAssets.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrendingAssets: () => (/* binding */ TrendingAssets)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Star,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ TrendingAssets auto */ \n\n\n// Mock trending assets data\nconst mockTrendingAssets = [\n    {\n        symbol: \"BTC\",\n        name: \"Bitcoin\",\n        price: 43250.50,\n        change24h: 2.4,\n        volume: 28500000000,\n        marketCap: 847000000000,\n        aiSignal: \"BUY\",\n        confidence: 78,\n        isWatched: true\n    },\n    {\n        symbol: \"ETH\",\n        name: \"Ethereum\",\n        price: 2650.75,\n        change24h: -1.2,\n        volume: 15200000000,\n        marketCap: 318000000000,\n        aiSignal: \"HOLD\",\n        confidence: 62,\n        isWatched: true\n    },\n    {\n        symbol: \"BNB\",\n        name: \"BNB\",\n        price: 315.20,\n        change24h: 0.8,\n        volume: 1800000000,\n        marketCap: 47000000000,\n        aiSignal: \"BUY\",\n        confidence: 72,\n        isWatched: false\n    },\n    {\n        symbol: \"SOL\",\n        name: \"Solana\",\n        price: 98.45,\n        change24h: 5.2,\n        volume: 2100000000,\n        marketCap: 42000000000,\n        aiSignal: \"BUY\",\n        confidence: 85,\n        isWatched: false\n    },\n    {\n        symbol: \"ADA\",\n        name: \"Cardano\",\n        price: 0.485,\n        change24h: -2.1,\n        volume: 890000000,\n        marketCap: 17000000000,\n        aiSignal: \"SELL\",\n        confidence: 68,\n        isWatched: false\n    }\n];\nfunction TrendingAssets() {\n    const handleWatchToggle = (symbol)=>{\n        // This will be implemented with state management\n        console.log(`Toggle watch for ${symbol}`);\n    };\n    const getSignalColor = (signal)=>{\n        switch(signal){\n            case \"BUY\":\n                return \"text-success-600 bg-success-50 dark:bg-success-900/20\";\n            case \"SELL\":\n                return \"text-danger-600 bg-danger-50 dark:bg-danger-900/20\";\n            case \"HOLD\":\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n            default:\n                return \"text-gray-600 bg-gray-50 dark:bg-gray-700\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-6 w-6 text-primary-600 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                children: \"Trending Assets\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300\",\n                            children: \"View All\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-b border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-left py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Asset\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"24h Change\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Volume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"AI Signal\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: mockTrendingAssets.map((asset, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: asset.symbol.charAt(0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                                children: asset.symbol\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: asset.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency)(asset.price)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatMarketCap)(asset.marketCap)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-center justify-end space-x-1 ${(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.getPriceChangeColor)(asset.change24h)}`,\n                                                children: [\n                                                    asset.change24h > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            asset.change24h > 0 ? \"+\" : \"\",\n                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatPercentage)(asset.change24h)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 dark:text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.formatMarketCap)(asset.volume)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `px-2 py-1 rounded-full text-xs font-medium ${getSignalColor(asset.aiSignal)}`,\n                                                        children: asset.aiSignal\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                        children: [\n                                                            asset.confidence,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleWatchToggle(asset.symbol),\n                                                        className: `p-1 rounded transition-colors ${asset.isWatched ? \"text-warning-500 hover:text-warning-600\" : \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"}`,\n                                                        title: asset.isWatched ? \"Remove from watchlist\" : \"Add to watchlist\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: `h-4 w-4 ${asset.isWatched ? \"fill-current\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-1 rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\",\n                                                        title: \"View details\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Star_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, asset.symbol, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/TrendingAssets.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/TrendingAssets.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun,TrendingUp,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\nfunction Header() {\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3); // This will come from store later\n    const toggleTheme = ()=>{\n        setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    className: \"lg:hidden p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center ml-4 lg:ml-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                    children: \"AI Sentinel\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"Adaptive Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleTheme,\n                                    className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    \"aria-label\": \"Toggle theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"relative p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this),\n                                        notificationCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 h-4 w-4 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                            children: notificationCount > 9 ? \"9+\" : notificationCount\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"U\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 bg-black bg-opacity-50\",\n                        onClick: ()=>setIsMobileMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: \"AI Sentinel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMobileMenuOpen(false),\n                                        className: \"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"block px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"block px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: \"Analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"block px-3 py-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                            children: \"Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Brain,ChevronLeft,ChevronRight,HelpCircle,Settings,Target,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        current: true\n    },\n    {\n        name: \"Market Analysis\",\n        href: \"/analysis\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        current: false\n    },\n    {\n        name: \"AI Insights\",\n        href: \"/insights\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        current: false\n    },\n    {\n        name: \"Alerts\",\n        href: \"/alerts\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        current: false,\n        badge: 3\n    },\n    {\n        name: \"Watchlist\",\n        href: \"/watchlist\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: \"Performance\",\n        href: \"/performance\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        current: false\n    }\n];\nconst bottomNavigation = [\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"Help\",\n        href: \"/help\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 lg:z-50 lg:bg-white lg:dark:bg-gray-800 lg:border-r lg:border-gray-200 lg:dark:border-gray-700 transition-all duration-300\", isCollapsed ? \"lg:w-16\" : \"lg:w-64\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-1 min-h-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsCollapsed(!isCollapsed),\n                                className: \"p-1.5 rounded-md text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\",\n                                children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Brain_ChevronLeft_ChevronRight_HelpCircle_Settings_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 pb-4 space-y-1\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors\", item.current ? \"bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100\" : \"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 h-5 w-5\", item.current ? \"text-primary-600 dark:text-primary-400\" : \"text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 flex-1\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 inline-block py-0.5 px-2 text-xs font-medium bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200 rounded-full\",\n                                                    children: item.badge\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 pb-4 space-y-1 border-t border-gray-200 dark:border-gray-700 pt-4\",\n                            children: bottomNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: item.href,\n                                    className: \"group flex items-center px-2 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 rounded-md hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"flex-shrink-0 h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-3\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-5 gap-1 p-2\",\n                    children: navigation.slice(0, 5).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex flex-col items-center justify-center p-2 text-xs font-medium rounded-md transition-colors\", item.current ? \"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20\" : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"h-5 w-5 mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"truncate w-full text-center\",\n                                    children: item.name.split(\" \")[0]\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -top-1 -right-1 h-4 w-4 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                    children: item.badge > 9 ? \"9+\" : item.badge\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   LoadingTable: () => (/* binding */ LoadingTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nfunction LoadingSpinner({ size = \"md\", className }) {\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-8 h-8\",\n        lg: \"w-12 h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600\", sizeClasses[size], className)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-200 dark:bg-gray-700 rounded\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingTable() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                [\n                    ...Array(5)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-16\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this)\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatMarketCap: () => (/* binding */ formatMarketCap),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatTimeAgo: () => (/* binding */ formatTimeAgo),\n/* harmony export */   formatVolume: () => (/* binding */ formatVolume),\n/* harmony export */   getConfidenceColor: () => (/* binding */ getConfidenceColor),\n/* harmony export */   getConfidenceLabel: () => (/* binding */ getConfidenceLabel),\n/* harmony export */   getMarketRegimeColor: () => (/* binding */ getMarketRegimeColor),\n/* harmony export */   getMarketRegimeLabel: () => (/* binding */ getMarketRegimeLabel),\n/* harmony export */   getPriceChangeColor: () => (/* binding */ getPriceChangeColor),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(value, currency = \"USD\", minimumFractionDigits = 2, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value);\n}\nfunction formatNumber(value, minimumFractionDigits = 0, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value);\n}\nfunction formatPercentage(value, minimumFractionDigits = 2, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"percent\",\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value / 100);\n}\nfunction formatMarketCap(value) {\n    if (value >= 1e12) {\n        return `$${(value / 1e12).toFixed(2)}T`;\n    } else if (value >= 1e9) {\n        return `$${(value / 1e9).toFixed(2)}B`;\n    } else if (value >= 1e6) {\n        return `$${(value / 1e6).toFixed(2)}M`;\n    } else if (value >= 1e3) {\n        return `$${(value / 1e3).toFixed(2)}K`;\n    }\n    return formatCurrency(value);\n}\nfunction formatVolume(value) {\n    return formatMarketCap(value);\n}\nfunction formatTimeAgo(timestamp) {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    if (days > 0) {\n        return `${days}d ago`;\n    } else if (hours > 0) {\n        return `${hours}h ago`;\n    } else if (minutes > 0) {\n        return `${minutes}m ago`;\n    } else {\n        return `${seconds}s ago`;\n    }\n}\nfunction getPriceChangeColor(change) {\n    if (change > 0) return \"text-success-600\";\n    if (change < 0) return \"text-danger-600\";\n    return \"text-gray-600\";\n}\nfunction getConfidenceColor(confidence) {\n    if (confidence >= 80) return \"text-success-600 bg-success-50\";\n    if (confidence >= 60) return \"text-warning-600 bg-warning-50\";\n    return \"text-danger-600 bg-danger-50\";\n}\nfunction getConfidenceLabel(confidence) {\n    if (confidence >= 80) return \"High\";\n    if (confidence >= 60) return \"Medium\";\n    return \"Low\";\n}\nfunction getMarketRegimeColor(regime) {\n    switch(regime.toLowerCase()){\n        case \"trending_up\":\n        case \"breakout\":\n            return \"bg-success-500\";\n        case \"trending_down\":\n            return \"bg-danger-500\";\n        case \"ranging\":\n        case \"consolidation\":\n            return \"bg-primary-500\";\n        case \"volatile\":\n            return \"bg-warning-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n}\nfunction getMarketRegimeLabel(regime) {\n    switch(regime.toLowerCase()){\n        case \"trending_up\":\n            return \"Trending Up\";\n        case \"trending_down\":\n            return \"Trending Down\";\n        case \"ranging\":\n            return \"Ranging\";\n        case \"volatile\":\n            return \"Volatile\";\n        case \"consolidation\":\n            return \"Consolidation\";\n        case \"breakout\":\n            return \"Breakout\";\n        default:\n            return \"Unknown\";\n    }\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c405cee33148\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VudGluZWxhLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83ZDgxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzQwNWNlZTMzMTQ4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Adaptive AI Sentinel - Cryptocurrency Analysis Platform\",\n    description: \"AI-powered cryptocurrency analysis platform with market regime detection and intelligent trading signals\",\n    keywords: [\n        \"cryptocurrency\",\n        \"trading\",\n        \"AI\",\n        \"technical analysis\",\n        \"market analysis\"\n    ],\n    authors: [\n        {\n            name: \"Adaptive AI Sentinel Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#0ea5e9\",\n    openGraph: {\n        title: \"Adaptive AI Sentinel\",\n        description: \"AI-powered cryptocurrency analysis platform\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Adaptive AI Sentinel\",\n        description: \"AI-powered cryptocurrency analysis platform\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/layout.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/layout.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/Dashboard */ \"(rsc)/./src/components/dashboard/Dashboard.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Sidebar */ \"(rsc)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/LoadingSpinner */ \"(rsc)/./src/components/ui/LoadingSpinner.tsx\");\n\n\n\n\n\n\nfunction HomePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__.Header, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6 lg:ml-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                            children: \"AI Sentinel Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-gray-600 dark:text-gray-400\",\n                                            children: \"Real-time cryptocurrency analysis with AI-powered market regime detection\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n                                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__.LoadingSpinner, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_2__.Dashboard, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/page.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBaUM7QUFDNEI7QUFDVDtBQUNFO0FBQ1U7QUFFakQsU0FBU0s7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDTCw2REFBTUE7Ozs7OzBCQUVQLDhEQUFDSTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNKLCtEQUFPQTs7Ozs7a0NBRVIsOERBQUNLO3dCQUFLRCxXQUFVO2tDQUNkLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0U7NENBQUdGLFdBQVU7c0RBQW1EOzs7Ozs7c0RBR2pFLDhEQUFDRzs0Q0FBRUgsV0FBVTtzREFBd0M7Ozs7Ozs7Ozs7Ozs4Q0FLdkQsOERBQUNQLDJDQUFRQTtvQ0FBQ1csd0JBQVUsOERBQUNQLHlFQUFjQTs7Ozs7OENBQ2pDLDRFQUFDSCxzRUFBU0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU94QiIsInNvdXJjZXMiOlsid2VicGFjazovL3NlbnRpbmVsYS1mcm9udGVuZC8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTdXNwZW5zZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IERhc2hib2FyZCB9IGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkJztcbmltcG9ydCB7IEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyJztcbmltcG9ydCB7IFNpZGViYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L1NpZGViYXInO1xuaW1wb3J0IHsgTG9hZGluZ1NwaW5uZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvTG9hZGluZ1NwaW5uZXInO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwIGRhcms6YmctZ3JheS05MDBcIj5cbiAgICAgIDxIZWFkZXIgLz5cbiAgICAgIFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XG4gICAgICAgIDxTaWRlYmFyIC8+XG4gICAgICAgIFxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgcC02IGxnOm1sLTY0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICBBSSBTZW50aW5lbCBEYXNoYm9hcmRcbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIFJlYWwtdGltZSBjcnlwdG9jdXJyZW5jeSBhbmFseXNpcyB3aXRoIEFJLXBvd2VyZWQgbWFya2V0IHJlZ2ltZSBkZXRlY3Rpb25cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17PExvYWRpbmdTcGlubmVyIC8+fT5cbiAgICAgICAgICAgICAgPERhc2hib2FyZCAvPlxuICAgICAgICAgICAgPC9TdXNwZW5zZT5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU3VzcGVuc2UiLCJEYXNoYm9hcmQiLCJIZWFkZXIiLCJTaWRlYmFyIiwiTG9hZGluZ1NwaW5uZXIiLCJIb21lUGFnZSIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iLCJoMSIsInAiLCJmYWxsYmFjayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/app/providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Dashboard: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/dashboard/Dashboard.tsx#Dashboard`);


/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Header.tsx#Header`);


/***/ }),

/***/ "(rsc)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sidebar: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/layout/Sidebar.tsx#Sidebar`);


/***/ }),

/***/ "(rsc)/./src/components/ui/LoadingSpinner.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/LoadingSpinner.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   LoadingTable: () => (/* binding */ LoadingTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nfunction LoadingSpinner({ size = \"md\", className }) {\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-8 h-8\",\n        lg: \"w-12 h-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600\", sizeClasses[size], className)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-32 bg-gray-200 dark:bg-gray-700 rounded\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingTable() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                [\n                    ...Array(5)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded w-16\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this)\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/sentinela-ia/frontend/src/components/ui/LoadingSpinner.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatMarketCap: () => (/* binding */ formatMarketCap),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatTimeAgo: () => (/* binding */ formatTimeAgo),\n/* harmony export */   formatVolume: () => (/* binding */ formatVolume),\n/* harmony export */   getConfidenceColor: () => (/* binding */ getConfidenceColor),\n/* harmony export */   getConfidenceLabel: () => (/* binding */ getConfidenceLabel),\n/* harmony export */   getMarketRegimeColor: () => (/* binding */ getMarketRegimeColor),\n/* harmony export */   getMarketRegimeLabel: () => (/* binding */ getMarketRegimeLabel),\n/* harmony export */   getPriceChangeColor: () => (/* binding */ getPriceChangeColor),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(value, currency = \"USD\", minimumFractionDigits = 2, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value);\n}\nfunction formatNumber(value, minimumFractionDigits = 0, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value);\n}\nfunction formatPercentage(value, minimumFractionDigits = 2, maximumFractionDigits = 2) {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"percent\",\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(value / 100);\n}\nfunction formatMarketCap(value) {\n    if (value >= 1e12) {\n        return `$${(value / 1e12).toFixed(2)}T`;\n    } else if (value >= 1e9) {\n        return `$${(value / 1e9).toFixed(2)}B`;\n    } else if (value >= 1e6) {\n        return `$${(value / 1e6).toFixed(2)}M`;\n    } else if (value >= 1e3) {\n        return `$${(value / 1e3).toFixed(2)}K`;\n    }\n    return formatCurrency(value);\n}\nfunction formatVolume(value) {\n    return formatMarketCap(value);\n}\nfunction formatTimeAgo(timestamp) {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const seconds = Math.floor(diff / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    if (days > 0) {\n        return `${days}d ago`;\n    } else if (hours > 0) {\n        return `${hours}h ago`;\n    } else if (minutes > 0) {\n        return `${minutes}m ago`;\n    } else {\n        return `${seconds}s ago`;\n    }\n}\nfunction getPriceChangeColor(change) {\n    if (change > 0) return \"text-success-600\";\n    if (change < 0) return \"text-danger-600\";\n    return \"text-gray-600\";\n}\nfunction getConfidenceColor(confidence) {\n    if (confidence >= 80) return \"text-success-600 bg-success-50\";\n    if (confidence >= 60) return \"text-warning-600 bg-warning-50\";\n    return \"text-danger-600 bg-danger-50\";\n}\nfunction getConfidenceLabel(confidence) {\n    if (confidence >= 80) return \"High\";\n    if (confidence >= 60) return \"Medium\";\n    return \"Low\";\n}\nfunction getMarketRegimeColor(regime) {\n    switch(regime.toLowerCase()){\n        case \"trending_up\":\n        case \"breakout\":\n            return \"bg-success-500\";\n        case \"trending_down\":\n            return \"bg-danger-500\";\n        case \"ranging\":\n        case \"consolidation\":\n            return \"bg-primary-500\";\n        case \"volatile\":\n            return \"bg-warning-500\";\n        default:\n            return \"bg-gray-500\";\n    }\n}\nfunction getMarketRegimeLabel(regime) {\n    switch(regime.toLowerCase()){\n        case \"trending_up\":\n            return \"Trending Up\";\n        case \"trending_down\":\n            return \"Trending Down\";\n        case \"ranging\":\n            return \"Ranging\";\n        case \"volatile\":\n            return \"Volatile\";\n        case \"consolidation\":\n            return \"Consolidation\";\n        case \"breakout\":\n            return \"Breakout\";\n        default:\n            return \"Unknown\";\n    }\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-hot-toast","vendor-chunks/next-themes","vendor-chunks/goober","vendor-chunks/lucide-react","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Fsentinela-ia%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();