"""
AI Analysis service with real technical indicators and pattern recognition
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import ta
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from app.core.config import settings
from app.services.unified_market_service import UnifiedMarketService
from loguru import logger


class AIAnalysisService:
    """Service for AI-powered cryptocurrency analysis"""
    
    def __init__(self, db: Session):
        self.db = db
        self.market_service = UnifiedMarketService(db)
        self.scaler = StandardScaler()
        
    async def get_technical_indicators(self, symbol: str, days: int = 100) -> Dict:
        """Calculate comprehensive technical indicators"""
        try:
            # Get price history
            price_data = await self.market_service.get_price_history(symbol, "1d", days)
            
            if len(price_data) < 20:  # Need minimum data for indicators
                raise Exception("Insufficient price data for analysis")
            
            # Convert to DataFrame
            df = pd.DataFrame(price_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            # Calculate technical indicators
            indicators = {}
            
            # Moving Averages
            indicators['sma_20'] = ta.trend.sma_indicator(df['close'], window=20).iloc[-1]
            indicators['sma_50'] = ta.trend.sma_indicator(df['close'], window=50).iloc[-1]
            indicators['ema_12'] = ta.trend.ema_indicator(df['close'], window=12).iloc[-1]
            indicators['ema_26'] = ta.trend.ema_indicator(df['close'], window=26).iloc[-1]
            
            # RSI
            rsi = ta.momentum.rsi(df['close'], window=14)
            indicators['rsi'] = rsi.iloc[-1]
            indicators['rsi_signal'] = self._get_rsi_signal(rsi.iloc[-1])
            
            # MACD
            macd_line = ta.trend.macd(df['close'])
            macd_signal = ta.trend.macd_signal(df['close'])
            macd_histogram = ta.trend.macd_diff(df['close'])
            indicators['macd'] = macd_line.iloc[-1]
            indicators['macd_signal'] = macd_signal.iloc[-1]
            indicators['macd_histogram'] = macd_histogram.iloc[-1]
            indicators['macd_signal_type'] = self._get_macd_signal(macd_line.iloc[-1], macd_signal.iloc[-1])
            
            # Bollinger Bands
            bb_high = ta.volatility.bollinger_hband(df['close'])
            bb_low = ta.volatility.bollinger_lband(df['close'])
            bb_mid = ta.volatility.bollinger_mavg(df['close'])
            current_price = df['close'].iloc[-1]
            
            indicators['bb_upper'] = bb_high.iloc[-1]
            indicators['bb_lower'] = bb_low.iloc[-1]
            indicators['bb_middle'] = bb_mid.iloc[-1]
            indicators['bb_position'] = (current_price - bb_low.iloc[-1]) / (bb_high.iloc[-1] - bb_low.iloc[-1])
            indicators['bb_signal'] = self._get_bb_signal(indicators['bb_position'])
            
            # Stochastic Oscillator
            stoch_k = ta.momentum.stoch(df['high'], df['low'], df['close'])
            stoch_d = ta.momentum.stoch_signal(df['high'], df['low'], df['close'])
            indicators['stoch_k'] = stoch_k.iloc[-1]
            indicators['stoch_d'] = stoch_d.iloc[-1]
            indicators['stoch_signal'] = self._get_stoch_signal(stoch_k.iloc[-1], stoch_d.iloc[-1])
            
            # Volume indicators
            indicators['volume_sma'] = ta.volume.volume_sma(df['close'], df['volume']).iloc[-1]
            indicators['volume_ratio'] = df['volume'].iloc[-1] / df['volume'].rolling(20).mean().iloc[-1]
            
            # Volatility
            indicators['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close']).iloc[-1]
            indicators['volatility'] = df['close'].pct_change().rolling(20).std().iloc[-1] * np.sqrt(365)
            
            # Support and Resistance levels
            support_resistance = self._calculate_support_resistance(df)
            indicators.update(support_resistance)
            
            return indicators
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators for {symbol}: {e}")
            raise Exception(f"Failed to calculate technical indicators: {str(e)}")
    
    async def detect_market_regime(self, symbol: str) -> Dict:
        """Detect current market regime using AI"""
        try:
            indicators = await self.get_technical_indicators(symbol)
            price_data = await self.market_service.get_price_history(symbol, "1d", 30)
            
            # Calculate regime features
            df = pd.DataFrame(price_data)
            df['returns'] = df['close'].pct_change()
            
            # Volatility regime
            volatility = df['returns'].std() * np.sqrt(365)
            
            # Trend strength
            sma_20 = indicators['sma_20']
            sma_50 = indicators['sma_50']
            current_price = df['close'].iloc[-1]
            
            trend_strength = abs(current_price - sma_50) / sma_50
            
            # Volume regime
            volume_ratio = indicators['volume_ratio']
            
            # Determine regime
            regime_data = {
                "regime": "unknown",
                "confidence": 0,
                "description": "",
                "indicators_used": ["volatility", "trend", "volume", "rsi", "macd"],
                "duration_hours": 24,  # Simplified for demo
                "characteristics": {}
            }
            
            # Regime classification logic
            if volatility > 0.8:  # High volatility
                if trend_strength > 0.1:
                    regime_data["regime"] = "volatile_trending"
                    regime_data["description"] = "High volatility with strong trend"
                    regime_data["confidence"] = 75
                else:
                    regime_data["regime"] = "volatile"
                    regime_data["description"] = "High volatility, no clear trend"
                    regime_data["confidence"] = 80
            elif trend_strength > 0.05:  # Clear trend
                if current_price > sma_20 > sma_50:
                    regime_data["regime"] = "trending_up"
                    regime_data["description"] = "Clear upward trend with low volatility"
                    regime_data["confidence"] = 85
                elif current_price < sma_20 < sma_50:
                    regime_data["regime"] = "trending_down"
                    regime_data["description"] = "Clear downward trend"
                    regime_data["confidence"] = 85
                else:
                    regime_data["regime"] = "ranging"
                    regime_data["description"] = "Sideways movement within range"
                    regime_data["confidence"] = 70
            else:  # Low volatility, weak trend
                regime_data["regime"] = "consolidation"
                regime_data["description"] = "Low volatility consolidation phase"
                regime_data["confidence"] = 75
            
            # Add characteristics
            regime_data["characteristics"] = {
                "volatility": volatility,
                "trend_strength": trend_strength,
                "volume_ratio": volume_ratio,
                "price_vs_sma20": (current_price - sma_20) / sma_20,
                "price_vs_sma50": (current_price - sma_50) / sma_50
            }
            
            return regime_data
            
        except Exception as e:
            logger.error(f"Error detecting market regime for {symbol}: {e}")
            raise Exception(f"Failed to detect market regime: {str(e)}")
    
    async def detect_chart_patterns(self, symbol: str) -> List[Dict]:
        """Detect chart patterns using technical analysis"""
        try:
            price_data = await self.market_service.get_price_history(symbol, "1d", 60)
            df = pd.DataFrame(price_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            
            patterns = []
            
            # Double Top/Bottom detection
            double_patterns = self._detect_double_patterns(df)
            patterns.extend(double_patterns)
            
            # Triangle patterns
            triangle_patterns = self._detect_triangle_patterns(df)
            patterns.extend(triangle_patterns)
            
            # Head and Shoulders
            hs_patterns = self._detect_head_shoulders(df)
            patterns.extend(hs_patterns)
            
            # Flag and Pennant patterns
            flag_patterns = self._detect_flag_patterns(df)
            patterns.extend(flag_patterns)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error detecting chart patterns for {symbol}: {e}")
            return []
    
    async def generate_ai_insights(self, symbol: str) -> Dict:
        """Generate comprehensive AI insights"""
        try:
            # Get all analysis components
            indicators = await self.get_technical_indicators(symbol)
            market_regime = await self.detect_market_regime(symbol)
            chart_patterns = await self.detect_chart_patterns(symbol)
            
            # Generate overall signal
            overall_signal, confidence = self._generate_overall_signal(indicators, market_regime, chart_patterns)
            
            # Generate explanation
            explanation = self._generate_explanation(symbol, indicators, market_regime, chart_patterns, overall_signal, confidence)
            
            # Calculate key levels
            key_levels = self._calculate_key_levels(indicators, chart_patterns)
            
            return {
                "symbol": symbol,
                "timestamp": int(datetime.now().timestamp()),
                "market_regime": market_regime,
                "technical_indicators": self._format_indicators_for_response(indicators),
                "chart_patterns": chart_patterns,
                "overall_signal": overall_signal,
                "confidence_score": confidence,
                "explanation": explanation,
                "key_levels": key_levels,
                "analysis_version": "1.0",
                "model_confidence": "high" if confidence > 75 else "medium" if confidence > 60 else "low"
            }
            
        except Exception as e:
            logger.error(f"Error generating AI insights for {symbol}: {e}")
            raise Exception(f"Failed to generate AI insights: {str(e)}")
    
    def _get_rsi_signal(self, rsi: float) -> str:
        """Get RSI signal"""
        if rsi > 70:
            return "SELL"
        elif rsi < 30:
            return "BUY"
        else:
            return "NEUTRAL"
    
    def _get_macd_signal(self, macd: float, signal: float) -> str:
        """Get MACD signal"""
        if macd > signal:
            return "BUY"
        else:
            return "SELL"
    
    def _get_bb_signal(self, position: float) -> str:
        """Get Bollinger Bands signal"""
        if position > 0.8:
            return "SELL"
        elif position < 0.2:
            return "BUY"
        else:
            return "NEUTRAL"
    
    def _get_stoch_signal(self, k: float, d: float) -> str:
        """Get Stochastic signal"""
        if k > 80 and d > 80:
            return "SELL"
        elif k < 20 and d < 20:
            return "BUY"
        else:
            return "NEUTRAL"

    def _calculate_support_resistance(self, df: pd.DataFrame) -> Dict:
        """Calculate support and resistance levels"""
        try:
            # Find local minima and maxima
            highs = df['high'].rolling(window=5, center=True).max()
            lows = df['low'].rolling(window=5, center=True).min()

            # Resistance levels (local highs)
            resistance_levels = []
            for i in range(2, len(df) - 2):
                if df['high'].iloc[i] == highs.iloc[i]:
                    resistance_levels.append(df['high'].iloc[i])

            # Support levels (local lows)
            support_levels = []
            for i in range(2, len(df) - 2):
                if df['low'].iloc[i] == lows.iloc[i]:
                    support_levels.append(df['low'].iloc[i])

            # Get most significant levels
            current_price = df['close'].iloc[-1]

            # Find nearest support and resistance
            resistance_above = [r for r in resistance_levels if r > current_price]
            support_below = [s for s in support_levels if s < current_price]

            nearest_resistance = min(resistance_above) if resistance_above else None
            nearest_support = max(support_below) if support_below else None

            return {
                "nearest_support": nearest_support,
                "nearest_resistance": nearest_resistance,
                "support_levels": sorted(support_levels, reverse=True)[:3],
                "resistance_levels": sorted(resistance_levels)[:3]
            }

        except Exception as e:
            logger.error(f"Error calculating support/resistance: {e}")
            return {
                "nearest_support": None,
                "nearest_resistance": None,
                "support_levels": [],
                "resistance_levels": []
            }

    def _detect_double_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Detect double top/bottom patterns"""
        patterns = []

        try:
            # Simplified double top/bottom detection
            highs = df['high'].rolling(window=5, center=True).max()
            lows = df['low'].rolling(window=5, center=True).min()

            # Look for double tops in last 30 days
            recent_highs = []
            for i in range(len(df) - 30, len(df) - 2):
                if i >= 2 and df['high'].iloc[i] == highs.iloc[i]:
                    recent_highs.append((i, df['high'].iloc[i]))

            # Check for double top pattern
            if len(recent_highs) >= 2:
                last_high = recent_highs[-1]
                second_last_high = recent_highs[-2]

                # Check if heights are similar (within 2%)
                height_diff = abs(last_high[1] - second_last_high[1]) / second_last_high[1]
                if height_diff < 0.02:
                    patterns.append({
                        "name": "Double Top",
                        "type": "bearish",
                        "confidence": 70,
                        "description": f"Double top pattern detected at ${last_high[1]:.2f}",
                        "target_price": last_high[1] * 0.95,  # 5% below
                        "stop_loss": last_high[1] * 1.02,     # 2% above
                        "formation_start": df.index[second_last_high[0]].timestamp(),
                        "formation_end": df.index[last_high[0]].timestamp()
                    })

            return patterns

        except Exception as e:
            logger.error(f"Error detecting double patterns: {e}")
            return []

    def _detect_triangle_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Detect triangle patterns"""
        patterns = []

        try:
            # Simplified ascending triangle detection
            recent_data = df.tail(20)

            # Check for ascending triangle (higher lows, similar highs)
            lows = recent_data['low']
            highs = recent_data['high']

            # Linear regression on lows (should be ascending)
            x = np.arange(len(lows))
            low_slope = np.polyfit(x, lows, 1)[0]

            # Check resistance level (similar highs)
            resistance_level = highs.max()
            high_touches = sum(1 for h in highs if abs(h - resistance_level) / resistance_level < 0.01)

            if low_slope > 0 and high_touches >= 2:
                patterns.append({
                    "name": "Ascending Triangle",
                    "type": "bullish",
                    "confidence": 65,
                    "description": f"Ascending triangle with resistance at ${resistance_level:.2f}",
                    "target_price": resistance_level * 1.05,
                    "stop_loss": lows.iloc[-1] * 0.98,
                    "formation_start": recent_data.index[0].timestamp(),
                    "formation_end": recent_data.index[-1].timestamp()
                })

            return patterns

        except Exception as e:
            logger.error(f"Error detecting triangle patterns: {e}")
            return []

    def _detect_head_shoulders(self, df: pd.DataFrame) -> List[Dict]:
        """Detect head and shoulders patterns"""
        # Simplified implementation - would need more sophisticated logic in production
        return []

    def _detect_flag_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Detect flag and pennant patterns"""
        patterns = []

        try:
            # Look for flag pattern: strong move followed by consolidation
            recent_data = df.tail(15)

            # Check for strong initial move (first 5 days)
            initial_move = recent_data.iloc[:5]
            price_change = (initial_move['close'].iloc[-1] - initial_move['close'].iloc[0]) / initial_move['close'].iloc[0]

            # Check for consolidation (last 10 days)
            consolidation = recent_data.iloc[5:]
            volatility = consolidation['close'].std() / consolidation['close'].mean()

            if abs(price_change) > 0.1 and volatility < 0.05:  # 10% move, low volatility
                pattern_type = "bullish" if price_change > 0 else "bearish"
                patterns.append({
                    "name": "Flag Pattern",
                    "type": pattern_type,
                    "confidence": 60,
                    "description": f"Flag pattern after {price_change*100:.1f}% move",
                    "target_price": recent_data['close'].iloc[-1] * (1 + price_change * 0.5),
                    "stop_loss": recent_data['close'].iloc[-1] * (1 - abs(price_change) * 0.3),
                    "formation_start": recent_data.index[0].timestamp(),
                    "formation_end": recent_data.index[-1].timestamp()
                })

            return patterns

        except Exception as e:
            logger.error(f"Error detecting flag patterns: {e}")
            return []

    def _generate_overall_signal(self, indicators: Dict, market_regime: Dict, chart_patterns: List[Dict]) -> Tuple[str, int]:
        """Generate overall trading signal with confidence"""
        try:
            signals = []
            weights = []

            # RSI signal
            rsi_signal = indicators.get('rsi_signal', 'NEUTRAL')
            if rsi_signal != 'NEUTRAL':
                signals.append(1 if rsi_signal == 'BUY' else -1)
                weights.append(0.2)

            # MACD signal
            macd_signal = indicators.get('macd_signal_type', 'NEUTRAL')
            if macd_signal != 'NEUTRAL':
                signals.append(1 if macd_signal == 'BUY' else -1)
                weights.append(0.25)

            # Bollinger Bands signal
            bb_signal = indicators.get('bb_signal', 'NEUTRAL')
            if bb_signal != 'NEUTRAL':
                signals.append(1 if bb_signal == 'BUY' else -1)
                weights.append(0.15)

            # Market regime influence
            regime = market_regime.get('regime', 'unknown')
            if regime in ['trending_up', 'breakout']:
                signals.append(1)
                weights.append(0.3)
            elif regime in ['trending_down']:
                signals.append(-1)
                weights.append(0.3)

            # Chart patterns influence
            bullish_patterns = sum(1 for p in chart_patterns if p.get('type') == 'bullish')
            bearish_patterns = sum(1 for p in chart_patterns if p.get('type') == 'bearish')

            if bullish_patterns > bearish_patterns:
                signals.append(1)
                weights.append(0.1)
            elif bearish_patterns > bullish_patterns:
                signals.append(-1)
                weights.append(0.1)

            # Calculate weighted average
            if signals and weights:
                weighted_signal = sum(s * w for s, w in zip(signals, weights)) / sum(weights)

                if weighted_signal > 0.3:
                    overall_signal = "BUY"
                    confidence = min(85, int(60 + weighted_signal * 25))
                elif weighted_signal < -0.3:
                    overall_signal = "SELL"
                    confidence = min(85, int(60 + abs(weighted_signal) * 25))
                else:
                    overall_signal = "HOLD"
                    confidence = 50
            else:
                overall_signal = "HOLD"
                confidence = 40

            return overall_signal, confidence

        except Exception as e:
            logger.error(f"Error generating overall signal: {e}")
            return "HOLD", 40

    def _generate_explanation(self, symbol: str, indicators: Dict, market_regime: Dict,
                            chart_patterns: List[Dict], signal: str, confidence: int) -> str:
        """Generate AI explanation of the analysis"""
        try:
            explanation_parts = []

            # Market regime explanation
            regime = market_regime.get('regime', 'unknown')
            regime_desc = market_regime.get('description', '')
            explanation_parts.append(f"{symbol} is currently in a '{regime}' market regime. {regime_desc}")

            # Technical indicators summary
            rsi = indicators.get('rsi', 0)
            macd_signal = indicators.get('macd_signal_type', 'NEUTRAL')

            if rsi > 70:
                explanation_parts.append(f"RSI at {rsi:.1f} indicates overbought conditions.")
            elif rsi < 30:
                explanation_parts.append(f"RSI at {rsi:.1f} suggests oversold conditions.")
            else:
                explanation_parts.append(f"RSI at {rsi:.1f} shows neutral momentum.")

            if macd_signal == 'BUY':
                explanation_parts.append("MACD shows bullish momentum with signal line crossover.")
            elif macd_signal == 'SELL':
                explanation_parts.append("MACD indicates bearish momentum.")

            # Chart patterns
            if chart_patterns:
                pattern_names = [p['name'] for p in chart_patterns]
                explanation_parts.append(f"Detected patterns: {', '.join(pattern_names)}.")

            # Support/Resistance
            support = indicators.get('nearest_support')
            resistance = indicators.get('nearest_resistance')

            if support and resistance:
                explanation_parts.append(f"Key support at ${support:.2f}, resistance at ${resistance:.2f}.")

            # Overall conclusion
            confidence_text = "high" if confidence > 75 else "medium" if confidence > 60 else "low"
            explanation_parts.append(f"AI recommends {signal} with {confidence_text} confidence ({confidence}%).")

            return " ".join(explanation_parts)

        except Exception as e:
            logger.error(f"Error generating explanation: {e}")
            return f"AI analysis for {symbol} completed with {signal} signal ({confidence}% confidence)."

    def _calculate_key_levels(self, indicators: Dict, chart_patterns: List[Dict]) -> Dict:
        """Calculate key price levels"""
        try:
            key_levels = {}

            # Support and resistance from indicators
            if indicators.get('nearest_support'):
                key_levels['support'] = indicators['nearest_support']

            if indicators.get('nearest_resistance'):
                key_levels['resistance'] = indicators['nearest_resistance']

            # Entry, target, and stop loss from patterns
            if chart_patterns:
                # Use the highest confidence pattern
                best_pattern = max(chart_patterns, key=lambda p: p.get('confidence', 0))

                if best_pattern.get('target_price'):
                    key_levels['target'] = best_pattern['target_price']

                if best_pattern.get('stop_loss'):
                    key_levels['stop_loss'] = best_pattern['stop_loss']

            return key_levels

        except Exception as e:
            logger.error(f"Error calculating key levels: {e}")
            return {}

    def _format_indicators_for_response(self, indicators: Dict) -> List[Dict]:
        """Format indicators for API response"""
        try:
            formatted = []

            # RSI
            if 'rsi' in indicators:
                formatted.append({
                    "name": "RSI",
                    "value": round(indicators['rsi'], 2),
                    "signal": indicators.get('rsi_signal', 'NEUTRAL'),
                    "confidence": 80,
                    "description": f"RSI at {indicators['rsi']:.1f}"
                })

            # MACD
            if 'macd' in indicators:
                formatted.append({
                    "name": "MACD",
                    "value": round(indicators['macd'], 4),
                    "signal": indicators.get('macd_signal_type', 'NEUTRAL'),
                    "confidence": 75,
                    "description": f"MACD line at {indicators['macd']:.4f}"
                })

            # Bollinger Bands
            if 'bb_position' in indicators:
                formatted.append({
                    "name": "Bollinger Bands",
                    "value": round(indicators['bb_position'], 2),
                    "signal": indicators.get('bb_signal', 'NEUTRAL'),
                    "confidence": 70,
                    "description": f"Price position: {indicators['bb_position']:.2f}"
                })

            # Stochastic
            if 'stoch_k' in indicators:
                formatted.append({
                    "name": "Stochastic",
                    "value": round(indicators['stoch_k'], 2),
                    "signal": indicators.get('stoch_signal', 'NEUTRAL'),
                    "confidence": 65,
                    "description": f"Stoch %K at {indicators['stoch_k']:.1f}"
                })

            return formatted

        except Exception as e:
            logger.error(f"Error formatting indicators: {e}")
            return []
